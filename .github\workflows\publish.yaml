name: Build and Push Dist

on:
  push:
    branches:
      - main

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: write # Required to push to the repository

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch full history to allow branch operations

      - uses: pnpm/action-setup@v4
        with:
          version: 9

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build package
        run: pnpm run build

      - name: Push dist to branch
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git checkout --orphan dist || git checkout dist
          git rm -rf . --quiet
          cp -r dist/* .
          git add .
          git commit -m "Update dist branch with build output" || echo "No changes to commit"
          git push origin dist --force
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}