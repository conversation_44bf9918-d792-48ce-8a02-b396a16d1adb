{"name": "@mallsurf-packages/core", "version": "1.0.0", "private": true, "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "publishConfig": {"registry": "https://npm.pkg.github.com", "@mallsurf-packages:registry": "https://npm.pkg.github.com", "access": "restricted"}, "scripts": {"build": "tsc && npm run prisma:generate && npm run prisma:copy", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:copy": "node scripts/copy-prisma.js", "publish": "npm publish"}, "keywords": [], "dependencies": {"@prisma/client": "^6.10.1", "axios": "^1.10.0", "prisma": "^6.10.1", "zod": "^3.25.67", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@types/node": "^24.0.4", "@types/react": "^19.1.8", "typescript": "^5.8.3"}}