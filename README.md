# @mallsurf-packages/core

A comprehensive TypeScript core package for the Mallsurf platform, providing database models, API utilities, and type-safe schemas for mall management systems.

## Overview

This package serves as the foundational layer for the Mallsurf ecosystem, offering:

- **Database Models**: Prisma-based models for users, malls, shops, events, offers, and analytics
- **Type Safety**: Auto-generated Zod schemas for runtime validation
- **API Utilities**: HTTP client configuration and response types
- **Multi-Environment Support**: Built for Node.js, browser, and edge environments

## Features

### 🏢 Mall Management
- **Mall Information**: Complete mall profiles with amenities, hours, and contact details
- **Floor Plans**: Multi-floor support with categorized shop layouts
- **Operating Hours**: Flexible scheduling for malls and individual shops

### 🏪 Shop Management
- **Shop Profiles**: Detailed shop information with categories and contact details
- **Shop Hours**: Individual operating schedules per shop
- **Location Mapping**: Coordinate-based shop positioning within malls

### 🎉 Events & Offers
- **Event Management**: Mall events with scheduling and location details
- **Promotional Offers**: Time-bound offers with terms and conditions
- **Shop-Offer Associations**: Link offers to specific shops

### 👥 User Management
- **Role-Based Access**: <PERSON><PERSON>, mall manager, shop owner, and user roles
- **User Profiles**: Complete user information with preferences
- **Authentication Support**: Login tracking and session management

### 📊 Analytics
- **Page View Tracking**: Detailed analytics for user interactions
- **Custom Reports**: Flexible reporting system with filters and date ranges
- **User Behavior**: Session tracking and engagement metrics

## Installation

```bash
npm install @mallsurf-packages/core
```

## Usage

### Database Client

```typescript
import prisma from '@mallsurf-packages/core/prisma';

// Query malls
const malls = await prisma.mall.findMany({
  include: {
    shops: true,
    events: true,
    floors: true
  }
});
```

### API Client

```typescript
import { getApiClient } from '@mallsurf-packages/core/api-client';

const apiClient = getApiClient({
  baseURL: 'https://api.mallsurf.com',
  headers: {
    'Authorization': 'Bearer your-token'
  },
  withCredentials: true
});
```

### Type-Safe Schemas

```typescript
import { MallSchema, ShopSchema, UserSchema } from '@mallsurf-packages/core/generated/zod';

// Validate mall data
const mallData = MallSchema.parse({
  name: "Central Mall",
  slug: "central-mall",
  city: "Downtown",
  parkingAvailable: true,
  parkingType: "free"
});
```

### API Response Types

```typescript
import { ApiResponse } from '@mallsurf-packages/core/types/api-response';

interface MallResponse extends ApiResponse<Mall[]> {
  data: Mall[];
  meta: {
    total: number;
    page: number;
    limit: number;
  };
}
```

## Database Schema

The package includes comprehensive models for:

- **User**: Authentication and profile management
- **Mall**: Mall information and management
- **Shop**: Individual shop details and management
- **Floor**: Mall floor plans and categorization
- **Event**: Mall events and activities
- **Offer**: Promotional offers and deals
- **PageView**: Analytics and tracking
- **Report**: Custom reporting system

## Development

### Building

```bash
# Install dependencies
pnpm install

# Generate Prisma client and Zod schemas
pnpm run build
```

### Database Operations

```bash
# Generate Prisma client
pnpm run prisma:generate

# Run database migrations
pnpm run prisma:migrate
```

## Publishing

The package is configured for GitHub Package Registry:

```bash
pnpm run publish
```

## Environment Support

This package supports multiple JavaScript environments:

- **Node.js**: Full Prisma client with database connectivity
- **Browser**: Browser-compatible client for frontend applications
- **Edge Runtime**: Optimized for edge computing environments
- **React Native**: Mobile application support

## Dependencies

- **@prisma/client**: Database ORM and client
- **zod**: Runtime type validation
- **zod-prisma-types**: Auto-generated Zod schemas from Prisma
- **axios**: HTTP client for API requests

## License

Private package for Mallsurf platform.

## Contributing

This is a private package. Please follow the established development workflow and ensure all changes are properly tested before publishing.
