const fs = require('fs');
const path = require('path');

/**
 * Recursively copies a directory from src to dest
 * @param {string} src - Source directory
 * @param {string} dest - Destination directory
 */
function copyDir(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }

    for (const entry of fs.readdirSync(src, { withFileTypes: true })) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);

        entry.isDirectory()
            ? copyDir(srcPath, destPath)
            : fs.copyFileSync(srcPath, destPath);
    }
}

const srcDir = path.resolve(__dirname, '../src/generated/prisma');
const destDir = path.resolve(__dirname, '../dist/generated/prisma');

console.log('📦 Copying Prisma generated files...');
console.log(`   From: ${srcDir}`);
console.log(`   To:   ${destDir}`);

try {
    if (!fs.existsSync(srcDir)) {
        console.warn('⚠️  Prisma source folder does not exist. Skipping copy.');
        process.exit(0);
    }

    copyDir(srcDir, destDir);
    console.log('✅ Prisma files copied successfully!');
} catch (error) {
    console.error('❌ Error copying Prisma files:\n', error.message);
    process.exit(1);
}
